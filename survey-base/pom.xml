<?xml version="1.0"?>
<project xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd"
         xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>cn.hanyi</groupId>
        <artifactId>parent-lite</artifactId>
        <version>${revision}.${sha1}-${changelist}</version>
        <relativePath>../pom.xml</relativePath>
    </parent>
    <artifactId>survey-base</artifactId>
    <version>${revision}.${sha1}-${changelist}</version>
    <name>survey-base</name>
    <properties>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <spring-boot.repackage.skip>true</spring-boot.repackage.skip>
    </properties>
    <dependencies>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-data-jpa</artifactId>
        </dependency>
        <dependency>
            <groupId>org.apache.commons</groupId>
            <artifactId>commons-collections4</artifactId>
            <version>4.4</version>
        </dependency>
        <dependency>
            <groupId>com.google.zxing</groupId>
            <artifactId>core</artifactId>
            <version>3.4.0</version>
        </dependency>
        <dependency>
            <groupId>com.google.zxing</groupId>
            <artifactId>javase</artifactId>
            <version>3.4.0</version>
        </dependency>
        <dependency>
            <groupId>cn.hanyi</groupId>
            <artifactId>ctm-common-core</artifactId>
        </dependency>
        <dependency>
            <groupId>cn.hanyi</groupId>
            <artifactId>ctm-customer-core</artifactId>
        </dependency>
        <dependency>
            <groupId>cn.hanyi</groupId>
            <artifactId>survey-core</artifactId>
        </dependency>
        <dependency>
            <groupId>cn.hanyi</groupId>
            <artifactId>survey-client</artifactId>
        </dependency>
        <dependency>
            <groupId>cn.hanyi</groupId>
            <artifactId>survey-quota</artifactId>
        </dependency>
        <dependency>
            <groupId>cn.hanyi</groupId>
            <artifactId>survey-trigger</artifactId>
        </dependency>
        <dependency>
            <groupId>cn.hanyi</groupId>
            <artifactId>cem-core</artifactId>
        </dependency>
        <dependency>
            <groupId>org.befun</groupId>
            <artifactId>befun-core</artifactId>
        </dependency>
        <dependency>
            <groupId>org.befun.extension</groupId>
            <artifactId>befun-x-pack</artifactId>
        </dependency>
        <dependency>
            <groupId>org.befun.task</groupId>
            <artifactId>befun-task</artifactId>
        </dependency>
        <dependency>
            <groupId>org.befun.auth</groupId>
            <artifactId>befun-auth-core</artifactId>
        </dependency>
        <dependency>
            <groupId>cn.hanyi.expression</groupId>
            <artifactId>survey-expression</artifactId>
        </dependency>
        <dependency>
            <groupId>com.alibaba</groupId>
            <artifactId>easyexcel</artifactId>
        </dependency>
    </dependencies>
</project>
